{"rustc": 15497389221046826682, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[9070360545695802481, "build_script_main", false, 5960180296091138159]], "local": [{"RerunIfChanged": {"output": "debug/build/openssl-sys-45adc491a99bc81e/output", "paths": ["/opt/homebrew/opt/openssl@3/include/openssl", "build/expando.c"]}}, {"RerunIfEnvChanged": {"var": "AARCH64_APPLE_DARWIN_OPENSSL_LIB_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "OPENSSL_LIB_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "AARCH64_APPLE_DARWIN_OPENSSL_INCLUDE_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "OPENSSL_INCLUDE_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "AARCH64_APPLE_DARWIN_OPENSSL_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "OPENSSL_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_aarch64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_aarch64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "MACOSX_DEPLOYMENT_TARGET", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_aarch64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_aarch64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "AARCH64_APPLE_DARWIN_OPENSSL_LIBS", "val": null}}, {"RerunIfEnvChanged": {"var": "OPENSSL_LIBS", "val": null}}, {"RerunIfEnvChanged": {"var": "AARCH64_APPLE_DARWIN_OPENSSL_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "OPENSSL_STATIC", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}