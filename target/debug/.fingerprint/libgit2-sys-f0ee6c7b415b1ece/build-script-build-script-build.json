{"rustc": 15497389221046826682, "features": "[\"https\", \"libssh2-sys\", \"openssl-sys\", \"ssh\", \"ssh_key_from_memory\"]", "declared_features": "[\"https\", \"libssh2-sys\", \"openssl-sys\", \"ssh\", \"ssh_key_from_memory\", \"vendored\", \"vendored-openssl\", \"zlib-ng-compat\"]", "target": 17883862002600103897, "profile": 3033921117576893, "path": 17920064118783052878, "deps": [[3214373357989284387, "pkg_config", false, 11635317295515557445], [3378925969027653845, "cc", false, 7322907827261379811]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/libgit2-sys-f0ee6c7b415b1ece/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}