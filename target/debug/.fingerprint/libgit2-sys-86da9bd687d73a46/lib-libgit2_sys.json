{"rustc": 15497389221046826682, "features": "[\"https\", \"libssh2-sys\", \"openssl-sys\", \"ssh\", \"ssh_key_from_memory\"]", "declared_features": "[\"https\", \"libssh2-sys\", \"openssl-sys\", \"ssh\", \"ssh_key_from_memory\", \"vendored\", \"vendored-openssl\", \"zlib-ng-compat\"]", "target": 8268026152342382216, "profile": 5347358027863023418, "path": 17556353820388644196, "deps": [[2924422107542798392, "libc", false, 12489402100633955943], [9070360545695802481, "openssl_sys", false, 12682564695000669182], [11857865261928459945, "build_script_build", false, 7617087191544827785], [17022423707615322322, "libz_sys", false, 12515363542090227746], [17372192778073352438, "libssh2_sys", false, 9929486128352519849]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/libgit2-sys-86da9bd687d73a46/dep-lib-libgit2_sys", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}