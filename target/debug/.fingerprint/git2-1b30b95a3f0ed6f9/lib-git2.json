{"rustc": 15497389221046826682, "features": "[\"default\", \"https\", \"openssl-probe\", \"openssl-sys\", \"ssh\", \"ssh_key_from_memory\"]", "declared_features": "[\"default\", \"https\", \"openssl-probe\", \"openssl-sys\", \"ssh\", \"ssh_key_from_memory\", \"unstable\", \"vendored-libgit2\", \"vendored-openssl\", \"zlib-ng-compat\"]", "target": 17727337184649825680, "profile": 5347358027863023418, "path": 3628925524361347364, "deps": [[2924422107542798392, "libc", false, 12489402100633955943], [3150220818285335163, "url", false, 13294482088631508234], [5986029879202738730, "log", false, 8611220590675663417], [7896293946984509699, "bitflags", false, 16027273537600711105], [11857865261928459945, "libgit2_sys", false, 14317872380287841904]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/git2-1b30b95a3f0ed6f9/dep-lib-git2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}