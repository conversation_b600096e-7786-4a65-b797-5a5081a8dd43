{"rustc": 15497389221046826682, "features": "[\"libc\"]", "declared_features": "[\"asm\", \"cmake\", \"default\", \"libc\", \"static\", \"stock-zlib\", \"zlib-ng\", \"zlib-ng-no-cmake-experimental-community-maintained\"]", "target": 17883862002600103897, "profile": 11143088632783682114, "path": 13665531211197375722, "deps": [[3214373357989284387, "pkg_config", false, 11635317295515557445], [3378925969027653845, "cc", false, 7322907827261379811], [12933202132622624734, "vcpkg", false, 6463112118625487937]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/libz-sys-9c320939a0b50c10/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}