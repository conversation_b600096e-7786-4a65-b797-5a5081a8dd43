{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"color\", \"color-auto\"]", "target": 16602908775176716730, "profile": 16450813782370704554, "path": 3852077134229779063, "deps": [[904119603456001782, "bstr", false, 2526346794697313158], [6491540798839599208, "predicates_core", false, 1646384754474538464], [9394696648929125047, "anstyle", false, 2574996990090726529], [12516616738327129663, "predicates_tree", false, 11378141212668861313], [12939671402123591185, "build_script_build", false, 17483283193583311677], [15863765456528386755, "predicates", false, 14656539466547602959], [17492147245553934378, "wait_timeout", false, 17796084267918250555], [18000218614148971598, "doc_comment", false, 9190152487175247044]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/assert_cmd-e8e43bdecf6967c6/dep-lib-assert_cmd", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}