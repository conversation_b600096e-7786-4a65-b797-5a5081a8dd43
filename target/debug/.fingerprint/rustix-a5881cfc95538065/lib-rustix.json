{"rustc": 15497389221046826682, "features": "[\"alloc\", \"default\", \"fs\", \"std\"]", "declared_features": "[\"all-apis\", \"alloc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"libc\", \"libc_errno\", \"linux_4_11\", \"linux_5_1\", \"linux_5_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"param\", \"pipe\", \"process\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 13113447432260090560, "path": 764994976794347973, "deps": [[2924422107542798392, "libc", false, 12489402100633955943], [7896293946984509699, "bitflags", false, 1866751995667517019], [12053020504183902936, "build_script_build", false, 16814293507918823591], [14633813869673313769, "libc_errno", false, 2807215750504216643]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustix-a5881cfc95538065/dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}