{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"openssl-on-win32\", \"openssl-sys\", \"vendored-openssl\", \"zlib-ng-compat\"]", "target": 8028971792975562915, "profile": 5347358027863023418, "path": 5686632740480329119, "deps": [[2924422107542798392, "libc", false, 12489402100633955943], [9070360545695802481, "openssl_sys", false, 12682564695000669182], [17022423707615322322, "libz_sys", false, 12515363542090227746], [17372192778073352438, "build_script_build", false, 4166165635360371573]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/libssh2-sys-5bc1a02d90ec3ad4/dep-lib-libssh2_sys", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}