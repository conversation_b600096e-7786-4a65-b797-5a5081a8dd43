{"rustc": 15497389221046826682, "features": "[\"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 5347358027863023418, "path": 17679598239911132922, "deps": [[555019317135488525, "regex_automata", false, 532914096440005667], [2779309023524819297, "aho_corasick", false, 3143235276521200052], [3129130049864710036, "memchr", false, 9165995699276581857], [9408802513701742484, "regex_syntax", false, 6984463678665853153]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-ad95243b422c7a31/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}