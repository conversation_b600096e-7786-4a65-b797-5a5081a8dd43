cargo:rerun-if-env-changed=LIBGIT2_NO_VENDOR
cargo:rerun-if-env-changed=LIBGIT2_NO_PKG_CONFIG
cargo:rerun-if-env-changed=PKG_CONFIG_aarch64-apple-darwin
cargo:rerun-if-env-changed=PKG_CONFIG_aarch64_apple_darwin
cargo:rerun-if-env-changed=HOST_PKG_CONFIG
cargo:rerun-if-env-changed=PKG_CONFIG
cargo:rerun-if-env-changed=LIBGIT2_STATIC
cargo:rerun-if-env-changed=LIBGIT2_DYNAMIC
cargo:rerun-if-env-changed=PKG_CONFIG_ALL_STATIC
cargo:rerun-if-env-changed=PKG_CONFIG_ALL_DYNAMIC
cargo:rerun-if-env-changed=PKG_CONFIG_PATH_aarch64-apple-darwin
cargo:rerun-if-env-changed=PKG_CONFIG_PATH_aarch64_apple_darwin
cargo:rerun-if-env-changed=HOST_PKG_CONFIG_PATH
cargo:rerun-if-env-changed=PKG_CONFIG_PATH
cargo:rerun-if-env-changed=PKG_CONFIG_LIBDIR_aarch64-apple-darwin
cargo:rerun-if-env-changed=PKG_CONFIG_LIBDIR_aarch64_apple_darwin
cargo:rerun-if-env-changed=HOST_PKG_CONFIG_LIBDIR
cargo:rerun-if-env-changed=PKG_CONFIG_LIBDIR
cargo:rerun-if-env-changed=PKG_CONFIG_SYSROOT_DIR_aarch64-apple-darwin
cargo:rerun-if-env-changed=PKG_CONFIG_SYSROOT_DIR_aarch64_apple_darwin
cargo:rerun-if-env-changed=HOST_PKG_CONFIG_SYSROOT_DIR
cargo:rerun-if-env-changed=PKG_CONFIG_SYSROOT_DIR
cargo:warning=failed to probe system libgit2: 
pkg-config exited with status code 1
> PKG_CONFIG_ALLOW_SYSTEM_LIBS=1 PKG_CONFIG_ALLOW_SYSTEM_CFLAGS=1 pkg-config --libs --cflags libgit2 'libgit2 >= 1.7.2' 'libgit2 < 1.8.0'

The system library `libgit2` required by crate `libgit2-sys` was not found.
The file `libgit2.pc` needs to be installed and the PKG_CONFIG_PATH environment variable must contain its parent directory.
The PKG_CONFIG_PATH environment variable is not set.

HINT: if you have installed the library, try setting PKG_CONFIG_PATH to the directory containing `libgit2.pc`.

cargo:rustc-cfg=libgit2_vendored
libgit2/include/git2.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2.h
libgit2/include/git2/signature.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/signature.h
libgit2/include/git2/oid.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/oid.h
libgit2/include/git2/index.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/index.h
libgit2/include/git2/email.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/email.h
libgit2/include/git2/ignore.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/ignore.h
libgit2/include/git2/attr.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/attr.h
libgit2/include/git2/blame.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/blame.h
libgit2/include/git2/oidarray.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/oidarray.h
libgit2/include/git2/pack.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/pack.h
libgit2/include/git2/revert.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/revert.h
libgit2/include/git2/version.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/version.h
libgit2/include/git2/odb.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/odb.h
libgit2/include/git2/status.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/status.h
libgit2/include/git2/net.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/net.h
libgit2/include/git2/tag.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/tag.h
libgit2/include/git2/annotated_commit.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/annotated_commit.h
libgit2/include/git2/config.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/config.h
libgit2/include/git2/branch.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/branch.h
libgit2/include/git2/types.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/types.h
libgit2/include/git2/repository.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/repository.h
libgit2/include/git2/clone.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/clone.h
libgit2/include/git2/global.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/global.h
libgit2/include/git2/blob.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/blob.h
libgit2/include/git2/cherrypick.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/cherrypick.h
libgit2/include/git2/mailmap.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/mailmap.h
libgit2/include/git2/submodule.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/submodule.h
libgit2/include/git2/errors.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/errors.h
libgit2/include/git2/message.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/message.h
libgit2/include/git2/merge.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/merge.h
libgit2/include/git2/pathspec.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/pathspec.h
libgit2/include/git2/tree.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/tree.h
libgit2/include/git2/odb_backend.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/odb_backend.h
libgit2/include/git2/graph.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/graph.h
libgit2/include/git2/describe.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/describe.h
libgit2/include/git2/rebase.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/rebase.h
libgit2/include/git2/worktree.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/worktree.h
libgit2/include/git2/stash.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/stash.h
libgit2/include/git2/remote.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/remote.h
libgit2/include/git2/strarray.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/strarray.h
libgit2/include/git2/reflog.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/reflog.h
libgit2/include/git2/cred_helpers.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/cred_helpers.h
libgit2/include/git2/buffer.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/buffer.h
libgit2/include/git2/diff.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/diff.h
libgit2/include/git2/sys/index.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/sys/index.h
libgit2/include/git2/sys/email.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/sys/email.h
libgit2/include/git2/sys/config.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/sys/config.h
libgit2/include/git2/sys/path.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/sys/path.h
libgit2/include/git2/sys/repository.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/sys/repository.h
libgit2/include/git2/sys/openssl.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/sys/openssl.h
libgit2/include/git2/sys/midx.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/sys/midx.h
libgit2/include/git2/sys/cred.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/sys/cred.h
libgit2/include/git2/sys/merge.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/sys/merge.h
libgit2/include/git2/sys/stream.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/sys/stream.h
libgit2/include/git2/sys/odb_backend.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/sys/odb_backend.h
libgit2/include/git2/sys/remote.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/sys/remote.h
libgit2/include/git2/sys/reflog.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/sys/reflog.h
libgit2/include/git2/sys/diff.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/sys/diff.h
libgit2/include/git2/sys/commit_graph.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/sys/commit_graph.h
libgit2/include/git2/sys/transport.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/sys/transport.h
libgit2/include/git2/sys/hashsig.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/sys/hashsig.h
libgit2/include/git2/sys/credential.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/sys/credential.h
libgit2/include/git2/sys/refs.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/sys/refs.h
libgit2/include/git2/sys/filter.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/sys/filter.h
libgit2/include/git2/sys/alloc.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/sys/alloc.h
libgit2/include/git2/sys/mempack.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/sys/mempack.h
libgit2/include/git2/sys/commit.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/sys/commit.h
libgit2/include/git2/sys/refdb_backend.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/sys/refdb_backend.h
libgit2/include/git2/trace.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/trace.h
libgit2/include/git2/common.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/common.h
libgit2/include/git2/proxy.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/proxy.h
libgit2/include/git2/indexer.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/indexer.h
libgit2/include/git2/refspec.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/refspec.h
libgit2/include/git2/notes.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/notes.h
libgit2/include/git2/cert.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/cert.h
libgit2/include/git2/deprecated.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/deprecated.h
libgit2/include/git2/transport.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/transport.h
libgit2/include/git2/revwalk.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/revwalk.h
libgit2/include/git2/patch.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/patch.h
libgit2/include/git2/object.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/object.h
libgit2/include/git2/checkout.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/checkout.h
libgit2/include/git2/apply.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/apply.h
libgit2/include/git2/revparse.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/revparse.h
libgit2/include/git2/credential_helpers.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/credential_helpers.h
libgit2/include/git2/credential.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/credential.h
libgit2/include/git2/refs.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/refs.h
libgit2/include/git2/reset.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/reset.h
libgit2/include/git2/filter.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/filter.h
libgit2/include/git2/transaction.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/transaction.h
libgit2/include/git2/experimental.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/experimental.h
libgit2/include/git2/stdint.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/stdint.h
libgit2/include/git2/commit.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/commit.h
libgit2/include/git2/refdb.h => /Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/include/git2/refdb.h
OPT_LEVEL = Some(0)
TARGET = Some(aarch64-apple-darwin)
HOST = Some(aarch64-apple-darwin)
cargo:rerun-if-env-changed=CC_aarch64-apple-darwin
CC_aarch64-apple-darwin = None
cargo:rerun-if-env-changed=CC_aarch64_apple_darwin
CC_aarch64_apple_darwin = None
cargo:rerun-if-env-changed=HOST_CC
HOST_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(true)
cargo:rerun-if-env-changed=MACOSX_DEPLOYMENT_TARGET
MACOSX_DEPLOYMENT_TARGET = None
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_aarch64_apple_darwin
CFLAGS_aarch64_apple_darwin = None
cargo:rerun-if-env-changed=CFLAGS_aarch64-apple-darwin
CFLAGS_aarch64-apple-darwin = None
CARGO_ENCODED_RUSTFLAGS = Some()
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
cargo:warning=libgit2/src/libgit2/refdb_fs.c:2478:18: warning: implicit truncation from 'int' to a one-bit wide bit-field changes value from 1 to -1 [-Wsingle-bit-bitfield-constant-conversion]
cargo:warning= 2478 |                 backend->fsync = 1;
cargo:warning=      |                                ^ ~
cargo:warning=1 warning generated.
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
cargo:warning=libgit2/src/util/net.c:490:26: warning: implicit truncation from 'int' to a one-bit wide bit-field changes value from 1 to -1 [-Wsingle-bit-bitfield-constant-conversion]
cargo:warning=  490 |                                         parser.hierarchical = 1;
cargo:warning=      |                                                             ^ ~
cargo:warning=libgit2/src/util/net.c:598:22: warning: implicit truncation from 'int' to a one-bit wide bit-field changes value from 1 to -1 [-Wsingle-bit-bitfield-constant-conversion]
cargo:warning=  598 |         parser.hierarchical = 1;
cargo:warning=      |                             ^ ~
cargo:warning=2 warnings generated.
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
cargo:warning=libgit2/src/libgit2/transports/smart_pkt.c:329:26: warning: implicit truncation from 'int' to a one-bit wide bit-field changes value from 1 to -1 [-Wsingle-bit-bitfield-constant-conversion]
cargo:warning=  329 |         data->seen_capabilities = 1;
cargo:warning=      |                                 ^ ~
cargo:warning=1 warning generated.
cargo:warning=libgit2/src/libgit2/transports/smart_protocol.c:270:35: warning: implicit truncation from 'int' to a one-bit wide bit-field changes value from 1 to -1 [-Wsingle-bit-bitfield-constant-conversion]
cargo:warning=  270 |         pkt_parse_data.seen_capabilities = 1;
cargo:warning=      |                                          ^ ~
cargo:warning=1 warning generated.
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
cargo:warning=libgit2/src/libgit2/streams/stransport.c:64:8: warning: 'SSLHandshake' is deprecated: first deprecated in macOS 10.15 - No longer supported. Use Network.framework. [-Wdeprecated-declarations]
cargo:warning=   64 |         ret = SSLHandshake(st->ctx);
cargo:warning=      |               ^
cargo:warning=/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/Security.framework/Headers/SecureTransport.h:1641:1: note: 'SSLHandshake' has been explicitly marked deprecated here
cargo:warning= 1641 | SSLHandshake                            (SSLContextRef          context)
cargo:warning=      | ^
cargo:warning=libgit2/src/libgit2/streams/stransport.c:73:13: warning: 'SSLCopyPeerTrust' is deprecated: first deprecated in macOS 10.15 - No longer supported. Use Network.framework. [-Wdeprecated-declarations]
cargo:warning=   73 |         if ((ret = SSLCopyPeerTrust(st->ctx, &trust)) != noErr)
cargo:warning=      |                    ^
cargo:warning=/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/Security.framework/Headers/SecureTransport.h:1261:1: note: 'SSLCopyPeerTrust' has been explicitly marked deprecated here
cargo:warning= 1261 | SSLCopyPeerTrust                        (SSLContextRef          context,
cargo:warning=      | ^
cargo:warning=libgit2/src/libgit2/streams/stransport.c:79:13: warning: 'SecTrustEvaluate' is deprecated: first deprecated in macOS 10.15 [-Wdeprecated-declarations]
cargo:warning=   79 |         if ((ret = SecTrustEvaluate(trust, &sec_res)) != noErr)
cargo:warning=      |                    ^~~~~~~~~~~~~~~~
cargo:warning=      |                    SecTrustEvaluateWithError
cargo:warning=/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/Security.framework/Headers/SecTrust.h:377:10: note: 'SecTrustEvaluate' has been explicitly marked deprecated here
cargo:warning=  377 | OSStatus SecTrustEvaluate(SecTrustRef trust, SecTrustResultType *result)
cargo:warning=      |          ^
cargo:warning=libgit2/src/libgit2/streams/stransport.c:111:13: warning: 'SSLCopyPeerTrust' is deprecated: first deprecated in macOS 10.15 - No longer supported. Use Network.framework. [-Wdeprecated-declarations]
cargo:warning=  111 |         if ((ret = SSLCopyPeerTrust(st->ctx, &trust)) != noErr)
cargo:warning=      |                    ^
cargo:warning=/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/Security.framework/Headers/SecureTransport.h:1261:1: note: 'SSLCopyPeerTrust' has been explicitly marked deprecated here
cargo:warning= 1261 | SSLCopyPeerTrust                        (SSLContextRef          context,
cargo:warning=      | ^
cargo:warning=libgit2/src/libgit2/streams/stransport.c:114:13: warning: 'SecTrustGetCertificateAtIndex' is deprecated: first deprecated in macOS 12.0 [-Wdeprecated-declarations]
cargo:warning=  114 |         sec_cert = SecTrustGetCertificateAtIndex(trust, 0);
cargo:warning=      |                    ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~
cargo:warning=      |                    SecTrustCopyCertificateChain
cargo:warning=/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/Security.framework/Headers/SecTrust.h:536:19: note: 'SecTrustGetCertificateAtIndex' has been explicitly marked deprecated here
cargo:warning=  536 | SecCertificateRef SecTrustGetCertificateAtIndex(SecTrustRef trust, CFIndex ix)
cargo:warning=      |                   ^
cargo:warning=libgit2/src/libgit2/streams/stransport.c:181:13: warning: 'SSLWrite' is deprecated: first deprecated in macOS 10.15 - No longer supported. Use Network.framework. [-Wdeprecated-declarations]
cargo:warning=  181 |         if ((ret = SSLWrite(st->ctx, data, data_len, &processed)) != noErr) {
cargo:warning=      |                    ^
cargo:warning=/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/Security.framework/Headers/SecureTransport.h:1670:1: note: 'SSLWrite' has been explicitly marked deprecated here
cargo:warning= 1670 | SSLWrite                                        (SSLContextRef          context,
cargo:warning=      | ^
cargo:warning=libgit2/src/libgit2/streams/stransport.c:238:13: warning: 'SSLRead' is deprecated: first deprecated in macOS 10.15 - No longer supported. Use Network.framework. [-Wdeprecated-declarations]
cargo:warning=  238 |         if ((ret = SSLRead(st->ctx, data, len, &processed)) != noErr) {
cargo:warning=      |                    ^
cargo:warning=/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/Security.framework/Headers/SecureTransport.h:1689:1: note: 'SSLRead' has been explicitly marked deprecated here
cargo:warning= 1689 | SSLRead                                         (SSLContextRef          context,
cargo:warning=      | ^
cargo:warning=libgit2/src/libgit2/streams/stransport.c:253:8: warning: 'SSLClose' is deprecated: first deprecated in macOS 10.15 - No longer supported. Use Network.framework. [-Wdeprecated-declarations]
cargo:warning=  253 |         ret = SSLClose(st->ctx);
cargo:warning=      |               ^
cargo:warning=/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/Security.framework/Headers/SecureTransport.h:1731:1: note: 'SSLClose' has been explicitly marked deprecated here
cargo:warning= 1731 | SSLClose                                        (SSLContextRef          context)
cargo:warning=      | ^
cargo:warning=libgit2/src/libgit2/streams/stransport.c:292:12: warning: 'SSLCreateContext' is deprecated: first deprecated in macOS 10.15 - No longer supported. Use Network.framework. [-Wdeprecated-declarations]
cargo:warning=  292 |         st->ctx = SSLCreateContext(NULL, kSSLClientSide, kSSLStreamType);
cargo:warning=      |                   ^
cargo:warning=/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/Security.framework/Headers/SecureTransport.h:316:1: note: 'SSLCreateContext' has been explicitly marked deprecated here
cargo:warning=  316 | SSLCreateContext(CFAllocatorRef __nullable alloc, SSLProtocolSide protocolSide, SSLConnectionType connectionType)
cargo:warning=      | ^
cargo:warning=libgit2/src/libgit2/streams/stransport.c:292:35: warning: 'kSSLClientSide' is deprecated: first deprecated in macOS 10.15 [-Wdeprecated-declarations]
cargo:warning=  292 |         st->ctx = SSLCreateContext(NULL, kSSLClientSide, kSSLStreamType);
cargo:warning=      |                                          ^
cargo:warning=/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/Security.framework/Headers/SecureTransport.h:218:5: note: 'kSSLClientSide' has been explicitly marked deprecated here
cargo:warning=  218 |     kSSLClientSide CF_ENUM_DEPRECATED(10_2, 10_15, 2_0, 13_0)
cargo:warning=      |     ^
cargo:warning=libgit2/src/libgit2/streams/stransport.c:292:51: warning: 'kSSLStreamType' is deprecated: first deprecated in macOS 10.15 [-Wdeprecated-declarations]
cargo:warning=  292 |         st->ctx = SSLCreateContext(NULL, kSSLClientSide, kSSLStreamType);
cargo:warning=      |                                                          ^
cargo:warning=/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/Security.framework/Headers/SecureTransport.h:223:5: note: 'kSSLStreamType' has been explicitly marked deprecated here
cargo:warning=  223 |     kSSLStreamType CF_ENUM_DEPRECATED(10_2, 10_15, 2_0, 13_0),
cargo:warning=      |     ^
cargo:warning=libgit2/src/libgit2/streams/stransport.c:299:13: warning: 'SSLSetIOFuncs' is deprecated: first deprecated in macOS 10.15 - No longer supported. Use Network.framework. [-Wdeprecated-declarations]
cargo:warning=  299 |         if ((ret = SSLSetIOFuncs(st->ctx, read_cb, write_cb)) != noErr ||
cargo:warning=      |                    ^
cargo:warning=/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/Security.framework/Headers/SecureTransport.h:418:1: note: 'SSLSetIOFuncs' has been explicitly marked deprecated here
cargo:warning=  418 | SSLSetIOFuncs                           (SSLContextRef          context,
cargo:warning=      | ^
cargo:warning=libgit2/src/libgit2/streams/stransport.c:300:13: warning: 'SSLSetConnection' is deprecated: first deprecated in macOS 10.15 - No longer supported. Use Network.framework. [-Wdeprecated-declarations]
cargo:warning=  300 |             (ret = SSLSetConnection(st->ctx, st)) != noErr ||
cargo:warning=      |                    ^
cargo:warning=/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/Security.framework/Headers/SecureTransport.h:657:1: note: 'SSLSetConnection' has been explicitly marked deprecated here
cargo:warning=  657 | SSLSetConnection                        (SSLContextRef                  context,
cargo:warning=      | ^
cargo:warning=libgit2/src/libgit2/streams/stransport.c:301:13: warning: 'SSLSetSessionOption' is deprecated: first deprecated in macOS 10.15 - No longer supported. Use Network.framework. [-Wdeprecated-declarations]
cargo:warning=  301 |             (ret = SSLSetSessionOption(st->ctx, kSSLSessionOptionBreakOnServerAuth, true)) != noErr ||
cargo:warning=      |                    ^
cargo:warning=/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/Security.framework/Headers/SecureTransport.h:384:1: note: 'SSLSetSessionOption' has been explicitly marked deprecated here
cargo:warning=  384 | SSLSetSessionOption                     (SSLContextRef          context,
cargo:warning=      | ^
cargo:warning=libgit2/src/libgit2/streams/stransport.c:301:42: warning: 'kSSLSessionOptionBreakOnServerAuth' is deprecated: first deprecated in macOS 10.15 [-Wdeprecated-declarations]
cargo:warning=  301 |             (ret = SSLSetSessionOption(st->ctx, kSSLSessionOptionBreakOnServerAuth, true)) != noErr ||
cargo:warning=      |                                                 ^
cargo:warning=/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/Security.framework/Headers/SecureTransport.h:99:2: note: 'kSSLSessionOptionBreakOnServerAuth' has been explicitly marked deprecated here
cargo:warning=   99 |         kSSLSessionOptionBreakOnServerAuth CF_ENUM_DEPRECATED(10_2, 10_15, 2_0, 13_0) = 0,
cargo:warning=      |         ^
cargo:warning=libgit2/src/libgit2/streams/stransport.c:302:13: warning: 'SSLSetProtocolVersionMin' is deprecated: first deprecated in macOS 10.15 - No longer supported. Use Network.framework. [-Wdeprecated-declarations]
cargo:warning=  302 |             (ret = SSLSetProtocolVersionMin(st->ctx, kTLSProtocol1)) != noErr ||
cargo:warning=      |                    ^
cargo:warning=/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/Security.framework/Headers/SecureTransport.h:458:1: note: 'SSLSetProtocolVersionMin' has been explicitly marked deprecated here
cargo:warning=  458 | SSLSetProtocolVersionMin  (SSLContextRef      context,
cargo:warning=      | ^
cargo:warning=libgit2/src/libgit2/streams/stransport.c:302:47: warning: 'kTLSProtocol1' is deprecated: first deprecated in macOS 10.15 [-Wdeprecated-declarations]
cargo:warning=  302 |             (ret = SSLSetProtocolVersionMin(st->ctx, kTLSProtocol1)) != noErr ||
cargo:warning=      |                                                      ^
cargo:warning=/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/Security.framework/Headers/SecProtocolTypes.h:157:5: note: 'kTLSProtocol1' has been explicitly marked deprecated here
cargo:warning=  157 |     kTLSProtocol1 CF_ENUM_DEPRECATED(10_2, 10_15, 5_0, 13_0) = 4,
cargo:warning=      |     ^
cargo:warning=libgit2/src/libgit2/streams/stransport.c:303:13: warning: 'SSLSetProtocolVersionMax' is deprecated: first deprecated in macOS 10.15 - No longer supported. Use Network.framework. [-Wdeprecated-declarations]
cargo:warning=  303 |             (ret = SSLSetProtocolVersionMax(st->ctx, kTLSProtocol12)) != noErr ||
cargo:warning=      |                    ^
cargo:warning=/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/Security.framework/Headers/SecureTransport.h:494:1: note: 'SSLSetProtocolVersionMax' has been explicitly marked deprecated here
cargo:warning=  494 | SSLSetProtocolVersionMax  (SSLContextRef      context,
cargo:warning=      | ^
cargo:warning=libgit2/src/libgit2/streams/stransport.c:303:47: warning: 'kTLSProtocol12' is deprecated: first deprecated in macOS 10.15 [-Wdeprecated-declarations]
cargo:warning=  303 |             (ret = SSLSetProtocolVersionMax(st->ctx, kTLSProtocol12)) != noErr ||
cargo:warning=      |                                                      ^
cargo:warning=/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/Security.framework/Headers/SecProtocolTypes.h:159:5: note: 'kTLSProtocol12' has been explicitly marked deprecated here
cargo:warning=  159 |     kTLSProtocol12 CF_ENUM_DEPRECATED(10_2, 10_15, 5_0, 13_0) = 8,
cargo:warning=      |     ^
cargo:warning=libgit2/src/libgit2/streams/stransport.c:304:13: warning: 'SSLSetPeerDomainName' is deprecated: first deprecated in macOS 10.15 - No longer supported. Use Network.framework. [-Wdeprecated-declarations]
cargo:warning=  304 |             (ret = SSLSetPeerDomainName(st->ctx, host, strlen(host))) != noErr) {
cargo:warning=      |                    ^
cargo:warning=/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/Security.framework/Headers/SecureTransport.h:686:1: note: 'SSLSetPeerDomainName' has been explicitly marked deprecated here
cargo:warning=  686 | SSLSetPeerDomainName            (SSLContextRef          context,
cargo:warning=      | ^
cargo:warning=libgit2/src/libgit2/streams/stransport.c:311:23: warning: implicit truncation from 'int' to a one-bit wide bit-field changes value from 1 to -1 [-Wsingle-bit-bitfield-constant-conversion]
cargo:warning=  311 |         st->parent.encrypted = 1;
cargo:warning=      |                              ^ ~
cargo:warning=21 warnings generated.
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
cargo:rerun-if-env-changed=AR_aarch64-apple-darwin
AR_aarch64-apple-darwin = None
cargo:rerun-if-env-changed=AR_aarch64_apple_darwin
AR_aarch64_apple_darwin = None
cargo:rerun-if-env-changed=HOST_AR
HOST_AR = None
cargo:rerun-if-env-changed=AR
AR = None
cargo:rerun-if-env-changed=ARFLAGS
ARFLAGS = None
cargo:rerun-if-env-changed=HOST_ARFLAGS
HOST_ARFLAGS = None
cargo:rerun-if-env-changed=ARFLAGS_aarch64_apple_darwin
ARFLAGS_aarch64_apple_darwin = None
cargo:rerun-if-env-changed=ARFLAGS_aarch64-apple-darwin
ARFLAGS_aarch64-apple-darwin = None
cargo:rustc-link-lib=static=git2
cargo:rustc-link-search=native=/Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out/build
cargo:root=/Users/<USER>/Workspace/burnit/target/debug/build/libgit2-sys-c76ff4ba29d05d2a/out
cargo:rustc-link-lib=iconv
cargo:rustc-link-lib=framework=Security
cargo:rustc-link-lib=framework=CoreFoundation
cargo:rerun-if-changed=libgit2/include
cargo:rerun-if-changed=libgit2/src
cargo:rerun-if-changed=libgit2/deps
