###########################################################################
# libssh2 installation details
#
# Copyright (C) The libssh2 project and its contributors.
# SPDX-License-Identifier: BSD-3-Clause
###########################################################################

prefix=/Users/<USER>/Workspace/burnit/target/debug/build/libssh2-sys-1618cb458119facc/out
exec_prefix=
libdir=/Users/<USER>/Workspace/burnit/target/debug/build/libssh2-sys-1618cb458119facc/out/lib
includedir=/Users/<USER>/Workspace/burnit/target/debug/build/libssh2-sys-1618cb458119facc/out/include

Name: libssh2
URL: https://libssh2.org/
Description: Library for SSH-based communication
Version: @LIBSSH2_VERSION@
Requires: @LIBSSH2_PC_REQUIRES@
Requires.private: @LIBSSH2_PC_REQUIRES_PRIVATE@
Libs: -L${libdir} -lssh2 @LIBSSH2_PC_LIBS@
Libs.private: @LIBSSH2_PC_LIBS_PRIVATE@
Cflags: -I${includedir}
