#!/bin/bash

# Script to create a test Git repository for burnit testing

set -e

REPO_DIR="test/fixtures/test_repo"

# Remove existing test repo if it exists
if [ -d "$REPO_DIR" ]; then
    rm -rf "$REPO_DIR"
fi

# Create test repository
mkdir -p "$REPO_DIR"
cd "$REPO_DIR"

# Initialize Git repository
git init

# Configure Git for testing
git config user.name "Test User"
git config user.email "<EMAIL>"

# Create initial commit
echo "# Test Repository" > README.md
echo "This is a test repository for burnit CLI testing." >> README.md
git add README.md
git commit -m "Initial commit"

# Create a simple test script
cat > test_script.sh << 'EOF'
#!/bin/bash
echo "Test script executed successfully!"
echo "Current directory: $(pwd)"
echo "Git branch: $(git branch --show-current 2>/dev/null || echo 'detached HEAD')"
echo "Git commit: $(git rev-parse HEAD)"
exit 0
EOF

chmod +x test_script.sh
git add test_script.sh
git commit -m "Add test script"

# Create a feature branch
git checkout -b feature/test-branch
echo "Feature branch content" > feature.txt
git add feature.txt
git commit -m "Add feature branch content"

# Create a tag
git tag v1.0.0

# Go back to main branch
git checkout main

# Create another commit on main
echo "Main branch update" >> README.md
git add README.md
git commit -m "Update README on main"

# Create another tag
git tag v1.1.0

echo "Test repository created successfully at $REPO_DIR"
echo "Available references:"
echo "- Branches: main, feature/test-branch"
echo "- Tags: v1.0.0, v1.1.0"
echo "- Commits: $(git log --oneline --all)"
