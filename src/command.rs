use crate::error::BurnitError;
use log::{debug, info};
use std::path::Path;
use std::process::{Command, Stdio};

/// Execute a command in the specified directory
pub fn execute_command(command: &str, working_dir: &Path) -> Result<(), BurnitError> {
    info!("Executing command: {} in directory: {:?}", command, working_dir);

    // Parse the command string into command and arguments
    let parts: Vec<&str> = command.split_whitespace().collect();
    if parts.is_empty() {
        return Err(BurnitError::CommandError("Empty command".to_string()));
    }

    let (cmd, args) = parts.split_first().unwrap();
    
    debug!("Command: {}, Args: {:?}", cmd, args);

    // Execute the command
    let mut child = Command::new(cmd)
        .args(args)
        .current_dir(working_dir)
        .stdout(Stdio::inherit())
        .stderr(Stdio::inherit())
        .spawn()
        .map_err(|e| BurnitError::CommandError(format!("Failed to start command '{}': {}", cmd, e)))?;

    // Wait for the command to complete
    let status = child.wait()
        .map_err(|e| BurnitError::CommandError(format!("Failed to wait for command: {}", e)))?;

    if status.success() {
        info!("Command completed successfully");
        Ok(())
    } else {
        let error_msg = match status.code() {
            Some(code) => format!("Command '{}' failed with exit code: {}", command, code),
            None => format!("Command '{}' was terminated by signal", command),
        };
        Err(BurnitError::CommandError(error_msg))
    }
}
