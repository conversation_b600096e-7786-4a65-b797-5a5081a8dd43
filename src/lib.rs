//! # Burnit
//!
//! A command-line tool that executes arbitrary commands against different Git references
//! (commits, branches, tags) in a local Git repository while safely managing Git state.
//!
//! ## Features
//!
//! - Execute commands against any Git reference (branch, commit SHA, tag)
//! - Automatically restore original Git state after execution
//! - Robust error handling and validation
//! - Structured logging for debugging and monitoring
//!
//! ## Safety
//!
//! Burnit ensures that the Git repository is always left in a consistent state,
//! even if the executed command fails or panics.

pub mod command;
pub mod error;
pub mod git;

pub use error::BurnitError;
