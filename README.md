# Burnit

A command-line tool that executes arbitrary commands against different Git references (commits, branches, tags) in a local Git repository while safely managing Git state.

## Features

- **Safe Git State Management**: Automatically stores and restores the original Git state
- **Flexible Reference Support**: Works with branches, commit SHAs, and tags
- **Robust Error Handling**: Comprehensive validation and error reporting
- **Structured Logging**: Detailed logging for debugging and monitoring
- **Cross-Platform**: Built with Rust for reliable cross-platform support

## Installation

### From Source

```bash
git clone https://github.com/your-username/burnit
cd burnit
cargo build --release
```

The binary will be available at `target/release/burnit`.

### Using Cargo

```bash
cargo install burnit
```

## Usage

```bash
burnit <repository_path> --command "<command_to_execute>" --ref <git_reference>
```

### Arguments

- `repository_path`: Path to the Git repository
- `--command, -c`: Command to execute in the repository directory
- `--ref, -r`: Git reference to checkout (branch, commit SHA, or tag)

### Examples

#### Execute command on a specific branch
```bash
burnit path/to/repo --command "npm test" --ref main
burnit path/to/repo --command "cargo build" --ref feature/new-api
```

#### Execute command on a specific commit
```bash
burnit path/to/repo --command "bin/benchmark" --ref cafd4cdcd3a99fbef6cb849db9d72afbc0841e33
burnit path/to/repo --command "make test" --ref abc123f
```

#### Execute command on a tag
```bash
burnit path/to/repo --command "bin/benchmark" --ref v1.0.0
burnit path/to/repo --command "npm run build" --ref release-2.1
```

#### Complex commands
```bash
burnit path/to/repo --command "cargo test --release -- --nocapture" --ref main
burnit path/to/repo --command "docker build -t myapp:test ." --ref feature/docker
```

## How It Works

1. **Validation**: Verifies that the repository path exists and is a valid Git repository
2. **State Capture**: Records the current Git state (branch/commit, uncommitted changes, repository state)
3. **Reference Resolution**: Resolves the specified reference (branch, commit, or tag)
4. **Checkout**: Safely checks out the target reference
5. **Command Execution**: Runs the specified command in the repository directory
6. **State Restoration**: Automatically restores the original Git state, even if the command fails

## Safety Guarantees

- **Always Restores State**: Uses RAII (Resource Acquisition Is Initialization) to ensure Git state is restored even if the process is interrupted
- **Uncommitted Changes Warning**: Warns when the original state had uncommitted changes
- **Safe Checkout**: Uses Git's safe checkout mode to prevent data loss
- **Error Recovery**: Handles various failure scenarios gracefully

## Error Handling

Burnit provides clear error messages for common issues:

- Repository not found or invalid
- Git reference doesn't exist
- Command execution failures
- Git operation errors
- Permission issues

## Logging

Set the `RUST_LOG` environment variable to control logging verbosity:

```bash
# Basic logging
RUST_LOG=info burnit path/to/repo --command "make test" --ref main

# Detailed logging
RUST_LOG=debug burnit path/to/repo --command "make test" --ref main

# All logging
RUST_LOG=trace burnit path/to/repo --command "make test" --ref main
```

## Development

### Prerequisites

- Rust 1.70 or later
- Git

### Building

```bash
cargo build
```

### Running Tests

```bash
# Run all tests
cargo test

# Run only unit tests
cargo test --lib

# Run only integration tests
cargo test --test integration_tests

# Run with logging
RUST_LOG=debug cargo test
```

### Test Repository

The project includes a test fixture repository at `test/fixtures/test_repo` that is automatically created by the setup script. This repository contains:

- Multiple branches (`main`, `feature/test-branch`)
- Tags (`v1.0.0`, `v1.1.0`)
- A test script for validation
- Various commits for testing different scenarios

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- Built with [clap](https://github.com/clap-rs/clap) for CLI argument parsing
- Uses [git2](https://github.com/rust-lang/git2-rs) for Git operations
- Error handling powered by [thiserror](https://github.com/dtolnay/thiserror) and [anyhow](https://github.com/dtolnay/anyhow)
