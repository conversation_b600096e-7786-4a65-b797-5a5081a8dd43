use assert_cmd::Command;
use predicates::prelude::*;
use std::path::PathBuf;
use tempfile::TempDir;

/// Get the path to the test repository fixture
fn test_repo_path() -> PathBuf {
    PathBuf::from("test/fixtures/test_repo")
}

/// Create a temporary copy of the test repository for isolated testing
fn create_temp_test_repo() -> TempDir {
    let temp_dir = TempDir::new().expect("Failed to create temp directory");
    let source = test_repo_path();
    let dest = temp_dir.path().join("test_repo");

    // Copy the test repository to temp directory
    copy_dir_all(&source, &dest).expect("Failed to copy test repository");

    temp_dir
}

/// Recursively copy a directory
fn copy_dir_all(src: &std::path::Path, dst: &std::path::Path) -> std::io::Result<()> {
    std::fs::create_dir_all(dst)?;
    for entry in std::fs::read_dir(src)? {
        let entry = entry?;
        let ty = entry.file_type()?;
        if ty.is_dir() {
            copy_dir_all(&entry.path(), &dst.join(entry.file_name()))?;
        } else {
            std::fs::copy(entry.path(), dst.join(entry.file_name()))?;
        }
    }
    Ok(())
}

#[test]
fn test_burnit_help() {
    let mut cmd = Command::cargo_bin("burnit").unwrap();
    cmd.arg("--help");

    cmd.assert()
        .success()
        .stdout(predicate::str::contains("Execute commands against different Git references"))
        .stdout(predicate::str::contains("--command"))
        .stdout(predicate::str::contains("--ref"));
}

#[test]
fn test_burnit_version() {
    let mut cmd = Command::cargo_bin("burnit").unwrap();
    cmd.arg("--version");

    cmd.assert()
        .success()
        .stdout(predicate::str::contains("burnit"));
}

#[test]
fn test_execute_command_on_main_branch() {
    let temp_dir = create_temp_test_repo();
    let repo_path = temp_dir.path().join("test_repo");

    let mut cmd = Command::cargo_bin("burnit").unwrap();
    cmd.arg(&repo_path)
        .arg("--command")
        .arg("./test_script.sh")
        .arg("--ref")
        .arg("main");

    cmd.assert()
        .success()
        .stdout(predicate::str::contains("Test script executed successfully!"));
}

#[test]
fn test_execute_command_on_feature_branch() {
    let temp_dir = create_temp_test_repo();
    let repo_path = temp_dir.path().join("test_repo");

    let mut cmd = Command::cargo_bin("burnit").unwrap();
    cmd.arg(&repo_path)
        .arg("--command")
        .arg("./test_script.sh")
        .arg("--ref")
        .arg("feature/test-branch");

    cmd.assert()
        .success()
        .stdout(predicate::str::contains("Test script executed successfully!"));
}

#[test]
fn test_execute_command_on_tag() {
    let temp_dir = create_temp_test_repo();
    let repo_path = temp_dir.path().join("test_repo");

    let mut cmd = Command::cargo_bin("burnit").unwrap();
    cmd.arg(&repo_path)
        .arg("--command")
        .arg("./test_script.sh")
        .arg("--ref")
        .arg("v1.0.0");

    cmd.assert()
        .success()
        .stdout(predicate::str::contains("Test script executed successfully!"));
}

#[test]
fn test_execute_command_on_commit_sha() {
    let temp_dir = create_temp_test_repo();
    let repo_path = temp_dir.path().join("test_repo");

    // Get the commit SHA of the initial commit
    let output = std::process::Command::new("git")
        .args(&["log", "--oneline", "--reverse"])
        .current_dir(&repo_path)
        .output()
        .expect("Failed to get git log");

    let log_output = String::from_utf8(output.stdout).unwrap();
    let first_line = log_output.lines().next().unwrap();
    let commit_sha = first_line.split_whitespace().next().unwrap();

    let mut cmd = Command::cargo_bin("burnit").unwrap();
    cmd.arg(&repo_path)
        .arg("--command")
        .arg("cat README.md") // Use a command that works with the initial commit
        .arg("--ref")
        .arg(commit_sha);

    cmd.assert()
        .success()
        .stdout(predicate::str::contains("Test Repository"));
}

#[test]
fn test_invalid_repository_path() {
    let mut cmd = Command::cargo_bin("burnit").unwrap();
    cmd.arg("/nonexistent/path")
        .arg("--command")
        .arg("echo test")
        .arg("--ref")
        .arg("main");

    cmd.assert()
        .failure()
        .stderr(predicate::str::contains("Repository not found"));
}

#[test]
fn test_invalid_git_reference() {
    let temp_dir = create_temp_test_repo();
    let repo_path = temp_dir.path().join("test_repo");

    let mut cmd = Command::cargo_bin("burnit").unwrap();
    cmd.arg(&repo_path)
        .arg("--command")
        .arg("echo test")
        .arg("--ref")
        .arg("nonexistent-branch");

    cmd.assert()
        .failure()
        .stderr(predicate::str::contains("Git reference not found"));
}

#[test]
fn test_command_failure() {
    let temp_dir = create_temp_test_repo();
    let repo_path = temp_dir.path().join("test_repo");

    let mut cmd = Command::cargo_bin("burnit").unwrap();
    cmd.arg(&repo_path)
        .arg("--command")
        .arg("false") // Command that always fails
        .arg("--ref")
        .arg("main");

    cmd.assert()
        .failure()
        .stderr(predicate::str::contains("Command execution failed"));
}

#[test]
fn test_git_state_restoration() {
    let temp_dir = create_temp_test_repo();
    let repo_path = temp_dir.path().join("test_repo");

    // Get the current branch before running burnit
    let output = std::process::Command::new("git")
        .args(&["branch", "--show-current"])
        .current_dir(&repo_path)
        .output()
        .expect("Failed to get current branch");
    let original_branch = String::from_utf8(output.stdout).unwrap().trim().to_string();

    // Run burnit on a different branch
    let mut cmd = Command::cargo_bin("burnit").unwrap();
    cmd.arg(&repo_path)
        .arg("--command")
        .arg("echo test")
        .arg("--ref")
        .arg("feature/test-branch");

    cmd.assert().success();

    // Check that we're back on the original branch
    let output = std::process::Command::new("git")
        .args(&["branch", "--show-current"])
        .current_dir(&repo_path)
        .output()
        .expect("Failed to get current branch after burnit");
    let current_branch = String::from_utf8(output.stdout).unwrap().trim().to_string();

    assert_eq!(original_branch, current_branch, "Git state was not properly restored");
}

#[test]
fn test_missing_required_arguments() {
    // Test missing repository path
    let mut cmd = Command::cargo_bin("burnit").unwrap();
    cmd.arg("--command").arg("echo test").arg("--ref").arg("main");
    cmd.assert().failure();

    // Test missing command
    let mut cmd = Command::cargo_bin("burnit").unwrap();
    cmd.arg("/tmp").arg("--ref").arg("main");
    cmd.assert().failure();

    // Test missing ref
    let mut cmd = Command::cargo_bin("burnit").unwrap();
    cmd.arg("/tmp").arg("--command").arg("echo test");
    cmd.assert().failure();
}
