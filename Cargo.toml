[package]
name = "burnit"
version = "0.1.0"
edition = "2021"
description = "Execute commands against different Git references while safely managing Git state"
authors = ["<PERSON> <<EMAIL>>"]
license = "MIT"

[[bin]]
name = "burnit"
path = "src/main.rs"

[dependencies]
clap = { version = "4.4", features = ["derive"] }
git2 = "0.18"
anyhow = "1.0"
log = "0.4"
env_logger = "0.10"
thiserror = "1.0"

[dev-dependencies]
tempfile = "3.8"
assert_cmd = "2.0"
predicates = "3.0"
